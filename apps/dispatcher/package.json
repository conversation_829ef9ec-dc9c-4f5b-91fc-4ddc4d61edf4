{"name": "@libra/dispatcher", "version": "1.0.0", "type": "module", "scripts": {"dev": "wrangler dev --port 3007 --persist-to=../web/.wrangler/state", "deploy": "bun with-env wrangler  deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "typecheck": "tsc --noEmit", "with-env": "dotenv -e .env --", "update": "bun update"}, "dependencies": {"drizzle-orm": "^0.44.3", "@libra/db": "*", "@libra/common": "*", "@libra/middleware": "*", "hono": "^4.8.9", "pg": "^8.16.3", "zod": "^3.25.76", "@hono/zod-openapi": "^0.19.10", "@scalar/hono-api-reference": "^0.9.12"}, "devDependencies": {"wrangler": "^4.26.0", "@cloudflare/workers-types": "^4.20250726.0", "typescript": "^5.8.3", "dotenv": "^17.2.1", "dotenv-cli": "^8.0.0", "@libra/typescript-config": "*"}}